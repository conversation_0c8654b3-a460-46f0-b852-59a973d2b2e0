// ignore_for_file: unused_import, deprecated_member_use

import 'dart:io' show Platform;
import 'package:google_api_availability/google_api_availability.dart';

import './firebase_options.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:bitsdojo_window/bitsdojo_window.dart';
import 'package:desktop_webview_window_for_is/desktop_webview_window_for_is.dart';
import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_easyloading/flutter_easyloading.dart';
import 'package:flutter_localizations/flutter_localizations.dart';
import 'package:provider/provider.dart';
import 'package:tencent_calls_uikit/tencent_calls_uikit.dart';
import 'package:tencent_chat_i18n_tool/language_json/strings.g.dart';
import 'package:tencent_chat_i18n_tool/tools/i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/custom_animation.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/models/language_local.dart';
import 'package:tencent_cloud_chat_demo/src/pages/app.dart';
import 'package:tencent_cloud_chat_demo/src/pages/register.dart';
import 'package:tencent_cloud_chat_demo/src/provider/custom_sticker_package.dart';
import 'package:tencent_cloud_chat_demo/src/provider/local_setting.dart';
import 'package:tencent_cloud_chat_demo/src/provider/login_user_Info.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/provider/user_guide_provider.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/theme/tui_theme.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/platform.dart';
import 'package:tencent_cloud_chat_demo/http/dio_instance.dart';
import 'package:tencent_cloud_chat_demo/src/provider/wallet_provider.dart';
import 'package:tencent_cloud_chat_demo/src/provider/cover_provide.dart';

void main(List<String> args) async {
  debugPrint('args: $args');
  // main函数中有await时，需要先调用此方法否则会有警告
  // 集成firebase
  // 判断是否支持谷歌三大件
  WidgetsFlutterBinding.ensureInitialized();
  GooglePlayServicesAvailability? availability;
  if (Platform.isAndroid) {
    availability = await GoogleApiAvailability.instance
        .checkGooglePlayServicesAvailability();
  }
  // 安卓不支持谷歌三件套的手机，不使用firebase
  if (availability?.value != 5) {
    await Firebase.initializeApp(options: DefaultFirebaseOptions.currentPlatform);
  }

  WidgetsFlutterBinding.ensureInitialized();
  // 设置状态栏样式
  SystemUiOverlayStyle style = SystemUiOverlayStyle(
    statusBarColor: hexToColor('ededed'),
  );
  SystemChrome.setSystemUIOverlayStyle(style);
  // 全局loading
  configLoading();

  // AutoSizeUtil.setStandard(375, isAutoTextSize: true);
  // fast i18n use device locale
  WidgetsFlutterBinding.ensureInitialized();
  LocaleSettings.useDeviceLocale();
   // 获取当前语言设置
  // final currentLocale = LocaleSettings.currentLocale;
  // debugPrint('currentLocale: $currentLocale');
  // LocaleSettings.setLocale(AppLocale.zhHant);
  // LocaleSettings.setLocale(AppLocale.zhHans);

      

  if (PlatformUtils().isAndroid) {
    SystemUiOverlayStyle systemUiOverlayStyle = const SystemUiOverlayStyle(
      statusBarColor: Colors.transparent,
    );
    SystemChrome.setSystemUIOverlayStyle(systemUiOverlayStyle);
  }

  SystemChrome.setPreferredOrientations([DeviceOrientation.portraitUp]).then((_) {
    runApp(
      // runAutoApp(
      TranslationProvider(
        child: MultiProvider(
          providers: [
            ChangeNotifierProvider(create: (_) => LoginUserInfo()),
            ChangeNotifierProvider(create: (_) => DefaultThemeData()),
            ChangeNotifierProvider(create: (_) => CustomStickerPackageData()),
            ChangeNotifierProvider(
              create: (_) => LocalSetting(),
            ),
            ChangeNotifierProvider(create: (_) => UserGuideProvider()),
            ChangeNotifierProvider(create: (_) => WalletProvider()),
            ChangeNotifierProvider(create: (_) => CoverProvider()),
          ],
          child: const TUIKitDemoApp(),
        ),
      ),
    );
  });

  if (PlatformUtils().isDesktop) {
    doWhenWindowReady(() {
      const initialSize = Size(1300, 830);
      appWindow.minSize = const Size(1100, 630);
      appWindow.size = initialSize;
      appWindow.alignment = Alignment.center;
      appWindow.show();
    });
  }

  //初始化网络请求
  DioInstance.instance().initDio(baseUrl: 'http://43.137.10.89/api');
}

class TUIKitDemoApp extends StatelessWidget {
  const TUIKitDemoApp({Key? key}) : super(key: key);
  @override
  Widget build(BuildContext context) {
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final localSetting = Provider.of<LocalSetting>(context, listen: true);
    I18nUtils(null, localSetting.language);
    // localSetting.language = LanguageEnum.zhHans.value;

    // 更新语言拦截器的 context 以获取最新的语言设置
    DioInstance.instance().updateLanguageContext(context);

    return MaterialApp(
      title: 'phichat',
      debugShowCheckedModeBanner: false,
      locale: TranslationProvider.of(context).flutterLocale, // use provider
      supportedLocales: LocaleSettings.supportedLocales,
      localizationsDelegates: GlobalMaterialLocalizations.delegates,
      navigatorKey: IMDemoConfig.navigatorKey,
      theme: ThemeData(

        platform: TargetPlatform.iOS,
        // pageTransitionsTheme: const PageTransitionsTheme(builders: {
        //   TargetPlatform.iOS: CupertinoPageTransitionsBuilder(),
        //   TargetPlatform.android: CupertinoPageTransitionsBuilder(),
        // }),
        pageTransitionsTheme: PageTransitionsTheme(
          builders: {
            // 为所有平台设置渐隐效果
            TargetPlatform.android: FadeTransitionBuilder(),
            TargetPlatform.iOS: FadeTransitionBuilder(),
            TargetPlatform.windows: FadeTransitionBuilder(),
            TargetPlatform.macOS: FadeTransitionBuilder(),
            TargetPlatform.linux: FadeTransitionBuilder(),
          },
        ),

        elevatedButtonTheme: ElevatedButtonThemeData(
            style: ElevatedButton.styleFrom(
          backgroundColor: theme.primaryColor,
        )),
      ),
      home: const TencentChatApp(),
      builder: EasyLoading.init(),
      navigatorObservers: !PlatformUtils().isMobile ? [] : [TUICallKit.navigatorObserver],
      routes: {
        '/register': (context) => const RegisterPage(),
      },
    );
  }
}

void configLoading() {
  EasyLoading.instance
    ..displayDuration = const Duration(milliseconds: 2000)
    ..indicatorType = EasyLoadingIndicatorType.fadingCircle
    ..loadingStyle = EasyLoadingStyle.dark
    ..indicatorSize = 45.0
    ..radius = 10.0
    ..progressColor = Colors.yellow
    ..backgroundColor = Colors.green
    ..indicatorColor = Colors.yellow
    ..textColor = Colors.yellow
    ..maskColor = Colors.blue.withOpacity(0.5)
    ..userInteractions = true
    ..dismissOnTap = false
    ..customAnimation = CustomAnimation();
}

class FadeTransitionBuilder extends PageTransitionsBuilder {
  @override
  Widget buildTransitions<T>(
      PageRoute<T> route,
      BuildContext context,
      Animation<double> animation,
      Animation<double> secondaryAnimation,
      Widget child,
      ) {
    // 使用缓动曲线让动画更自然
    var curvedAnimation = CurvedAnimation(
      parent: animation,
      curve: Curves.easeOut,
      reverseCurve: Curves.easeIn,
    );

    return FadeTransition(
      opacity: curvedAnimation,
      child: child,
    );
  }
}