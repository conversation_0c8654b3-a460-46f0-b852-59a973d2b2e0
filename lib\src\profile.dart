// ignore_for_file: unused_import

import 'dart:math';
import 'package:provider/provider.dart';
import 'package:flutter/material.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/about.dart';
import 'package:tencent_cloud_chat_demo/src/my_profile_detail.dart';
import 'package:tencent_cloud_chat_demo/src/pages/skin/skin_page.dart';
import 'package:tencent_cloud_chat_demo/utils/constant.dart';
import 'package:tencent_cloud_chat_sdk/manager/v2_tim_manager.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_conversation.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_friend_info.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_user_full_info.dart';
import 'package:tencent_cloud_chat_uikit/tencent_cloud_chat_uikit.dart';
import 'package:tencent_cloud_chat_demo/src/pages/login.dart';
import 'package:tencent_cloud_chat_demo/src/provider/local_setting.dart';
import 'package:tencent_cloud_chat_demo/src/provider/login_user_Info.dart';
import 'package:tencent_cloud_chat_demo/src/provider/theme.dart';
import 'package:tencent_cloud_chat_demo/src/routes.dart';
import 'package:tencent_cloud_chat_demo/utils/theme.dart';
import 'package:tencent_cloud_chat_demo/utils/toast.dart';
import 'package:adaptive_action_sheet/adaptive_action_sheet.dart';
import 'package:tencent_cloud_chat_uikit/theme/color.dart';
import 'package:tencent_cloud_chat_uikit/ui/utils/screen_utils.dart';
import 'package:tencent_cloud_chat_demo/config.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import './widgets/bottom_sheet_picker.dart';
import '../models/language_local.dart';
import './provider/cover_provide.dart';
import 'package:tencent_cloud_chat_demo/apis/tuils_api.dart';
import './widgets/confirm_dialog.dart';
import '../utils/database_helper.dart';

class MyProfileSetting extends StatefulWidget {
  const MyProfileSetting({Key? key}) : super(key: key);

  @override
  State<StatefulWidget> createState() => _ProfileSettingState();
}

class _ProfileSettingState extends State<MyProfileSetting> {
  final CoreServicesImpl _coreServices = TIMUIKitCore.getInstance();
  final V2TIMManager sdkInstance = TIMUIKitCore.getSDKInstance();
  final TIMUIKitProfileController _timuiKitProfileController =
      TIMUIKitProfileController();
  String? userID;

  String _getAllowText(int? allowType) {
    if (allowType == 0) {
      return TIM_t("允许任何人");
    }

    if (allowType == 1) {
      return TIM_t("需要验证信息");
    }

    if (allowType == 2) {
      return TIM_t("禁止加我为好友");
    }

    return TIM_t("未指定");
  }

  String _getLanguage(String type) {
    if (type == "zh") {
      return "简体中文";
    }
    if (type == "en") {
      return "English";
    }
    return "简体中文";
  }

  _handleLogout() async {
    final bool? confirm = await ConfirmDialog.show(
      context: context,
      title: TIM_t("是否退出登录?"),
    );

    if (confirm == true) {
      ToastUtils.showLoading();
      final res = await _coreServices
          .logout()
          .whenComplete(() => ToastUtils.hideLoading());
      debugPrint('click logout');
      if (res.code == 0) {
        debugPrint('logout success');
        try {
          Future<SharedPreferences> _prefs = SharedPreferences.getInstance();
          final coverProvider =
              Provider.of<CoverProvider>(context, listen: false);
          SharedPreferences prefs = await _prefs;
          prefs.remove(Const.DEV_LOGIN_USER_ID);
          prefs.remove(Const.DEV_LOGIN_USER_SIG);
          prefs.remove(Const.SMS_LOGIN_TOKEN);
          prefs.remove(Const.SMS_LOGIN_PHONE);
          // 清除本地存储的登录信息
          await UserInfoLocal.clearLoginInfo();
          // 清除红包封面类型
          await RedEnvelopeLocal.clearRedEnvelopeType();
          coverProvider.clearRedEnvelopeType();
          // 清除朋友圈
          await DatabaseHelper().clearMoments();
        } catch (err) {
          ToastUtils.log("someError");
          ToastUtils.log(err);
        }
        Navigator.of(context).pushAndRemoveUntil(
          MaterialPageRoute(
              builder: (BuildContext context) => const LoginPage()),
          (route) => false, // 清除所有路由历史
        );
      }
    }
  }

  changeFriendVerificationMethod(int allowType) async {
    _timuiKitProfileController.changeFriendVerificationMethod(allowType);
  }

  // 选择加我为好友的方式
  showApplicationTypeSheet(BuildContext context, theme, int? allowType) {
    const allowAny = 0;
    const neddConfirm = 1;
    const denyAny = 2;
    // 使用组件
    BottomSheetPicker.show(
      context: context,
      title: '',
      options: [
        BottomSheetOption(
          title: TIM_t("允许任何人"),
          isSelected: allowType == 0,
          onTap: () async {
            changeFriendVerificationMethod(allowAny);
          },
        ),
        BottomSheetOption(
          title: TIM_t("需要验证信息"),
          isSelected: allowType == 1,
          onTap: () async {
            changeFriendVerificationMethod(neddConfirm);
          },
        ),
        BottomSheetOption(
          title: TIM_t("禁止加我为好友"),
          isSelected: allowType == 2,
          onTap: () async {
            changeFriendVerificationMethod(denyAny);
          },
        ),
      ],
      cancelText: TIM_t("取消"),
    );
  }

  // 选择语言
  showLanguageSheet(BuildContext context, theme, int? allowType) {
    // 获取 LocalSetting 实例
    final localSetting = Provider.of<LocalSetting>(context, listen: false);
    final coverProvider = Provider.of<CoverProvider>(context, listen: false);
    // 使用组件
    BottomSheetPicker.show(
      context: context,
      title: TIM_t(''),
      options: [
        BottomSheetOption(
          title: TIM_t("简体中文"),
          isSelected: localSetting.language == LanguageEnum.zhHans.value,
          onTap: () async {
            I18nUtils(null, LanguageEnum.zhHans.value);
            localSetting.language = LanguageEnum.zhHans.value;
            coverProvider.clearRedEnvelopeType();
            coverProvider.getRedPacketRecord(context);
          },
        ),
        BottomSheetOption(
          title: TIM_t("英文"),
          isSelected: localSetting.language == LanguageEnum.en.value,
          onTap: () async {
            I18nUtils(null, LanguageEnum.en.value);
            localSetting.language = LanguageEnum.en.value;
            coverProvider.clearRedEnvelopeType();
            coverProvider.getRedPacketRecord(context);
          },
        ),
      ],
      cancelText: TIM_t("取消"),
    );
  }

  @override
  void initState() {
    super.initState();
  }

  @override
  Widget build(BuildContext context) {
    final LocalSetting localSetting = Provider.of<LocalSetting>(context);
    final themeType = Provider.of<DefaultThemeData>(context).currentThemeType;
    final theme = Provider.of<DefaultThemeData>(context).theme;
    final loginUserInfoModel = Provider.of<LoginUserInfo>(context);
    final bool isWideScreen =
        TUIKitScreenUtils.getFormFactor() == DeviceType.Desktop;
    final V2TimUserFullInfo loginUserInfo = loginUserInfoModel.loginUserInfo;
    return Scaffold(
      resizeToAvoidBottomInset: false,
      backgroundColor: const Color(0xFFF9F9F9),
      appBar: AppBar(
        title: Text(TIM_t('设置'),
            style: const TextStyle(fontSize: IMDemoConfig.appBarTitleFontSize)),
        backgroundColor: Colors.white,
      ),
      body: TIMUIKitProfile(
        isSelf: true,
        userID: loginUserInfo.userID ?? "",
        controller: _timuiKitProfileController,
        builder: (BuildContext context, V2TimFriendInfo userInfo,
            V2TimConversation conversation, int friendType, bool isMute) {
          final userProfile = userInfo.userProfile;
          final int? allowType = userProfile?.allowType;
          final allowText = _getAllowText(allowType);
          final language = _getLanguage(localSetting.language ?? "zh");
          return SingleChildScrollView(
              child: Column(
            children: [
              // 好友验证方式选
              Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: InkWell(
                  onTap: () {
                    showApplicationTypeSheet(
                        context, theme, userProfile?.allowType);
                  },
                  child: TIMUIKitOperationItem(
                    isEmpty: false,
                    operationName: TIM_t("加我为好友的方式"),
                    operationRightWidget: Text(
                      allowText,
                      textAlign: isWideScreen ? null : TextAlign.end,
                    ),
                  ),
                ),
              ),
              // 多语言切换
              Container(
                margin: const EdgeInsets.symmetric(vertical: 10),
                child: InkWell(
                  onTap: () {
                    showLanguageSheet(context, theme, userProfile?.allowType);
                  },
                  child: TIMUIKitOperationItem(
                    isEmpty: false,
                    operationName: TIM_t("语言"),
                    operationRightWidget: Text(
                      language,
                      textAlign: isWideScreen ? null : TextAlign.end,
                    ),
                  ),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              TIMUIKitOperationItem(
                isEmpty: false,
                operationName: TIM_t("消息阅读状态"),
                operationDescription:
                    TIM_t("关闭后，您收发的消息均不带消息阅读状态，您将无法看到对方是否已读，同时对方也无法看到你是否已读。"),
                type: "switch",
                operationValue: localSetting.isShowReadingStatus,
                onSwitchChange: (bool value) {
                  localSetting.isShowReadingStatus = value;
                  if (value) {
                    ToastUtils.toast(TIM_t("该功能为旗舰版功能"));
                  }
                },
              ),
              const SizedBox(
                height: 10,
              ),
              TIMUIKitOperationItem(
                isEmpty: false,
                operationName: TIM_t("显示在线状态"),
                operationDescription:
                    TIM_t("关闭后，您将不可以在会话列表和通讯录中看到好友在线或离线的状态提示。"),
                type: "switch",
                operationValue: localSetting.isShowOnlineStatus,
                onSwitchChange: (bool value) {
                  localSetting.isShowOnlineStatus = value;
                },
              ),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                onTap: () {
                  Navigator.push(
                    context,
                    MaterialPageRoute(
                      builder: (context) => const About(),
                    ),
                  );
                },
                child: TIMUIKitOperationItem(
                  isEmpty: false,
                  operationName: TIM_t("关于腾讯云 · IM"),
                  operationRightWidget: const Text(""),
                ),
              ),
              const SizedBox(
                height: 10,
              ),
              InkWell(
                onTap: _handleLogout,
                child: Container(
                  alignment: Alignment.center,
                  padding: const EdgeInsets.symmetric(vertical: 15),
                  decoration: BoxDecoration(
                      color: Colors.white,
                      border: Border(
                          bottom: BorderSide(color: hexToColor("E5E5E5")))),
                  child: Text(
                    TIM_t("退出登录"),
                    style: TextStyle(color: hexToColor("FF584C"), fontSize: 17),
                  ),
                ),
              )
            ],
          ));
        },
      ),
    );
  }
}
