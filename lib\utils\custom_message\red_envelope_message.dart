import 'dart:convert';

import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_custom_elem.dart';
// '{"redEnvelopeID":"red_envelope","isOpen":false,"image":"https://pic1.imgdb.cn/item/6858f1bc58cb8da5c864cbb4/base.png","remark":"${data['remark']}","amount":"${data['amount']}","version":4}'
//data:'{"redEnvelopeID":"red_envelope","isOpen":false,"image":"$url","remark":"${data.remark}","amount":"${data.amount}",
//"isDefault":${sort == 0},"redPacketId":"$redPacketId","version":4, "type": "${data.chatType}", "totalCount":"${data.totalCount}", "chatType":${data.chatType}}',
class RedEnvelopeMessage {
  String? image;
  String? remark;
  String? amount;
  String? redEnvelopeID;
  bool? isOpen;
  bool? isDefault;
  String? redPacketId;
  String? type;
  String? totalCount;
  String? chatType;
  List<dynamic>? openUsers;

  RedEnvelopeMessage.fromJSON(Map json) {
    image = json["image"];
    remark = json["remark"];
    amount = json["amount"];
    redEnvelopeID = json["redEnvelopeID"];  
    isOpen = json["isOpen"] ?? false; 
    isDefault = json["isDefault"] ?? false;
    redPacketId = json["redPacketId"];
    type = json["type"];
    totalCount = json["totalCount"];
    chatType = json["chatType"];
    openUsers = json["openUsers"];
  }
}

RedEnvelopeMessage? getRedEnvelopeMessage(V2TimCustomElem? customElem) {
  try {
    if (customElem?.data != null) {

      final customMessage = jsonDecode(customElem!.data!);

      if (customMessage['redEnvelopeID'] != null) {
        
        return RedEnvelopeMessage.fromJSON(customMessage);
      }else {
        return null;
      }
    }
    return null;
  } catch (err) {
    debugPrint('errerrerrerrerr: $err');
    return null;
  }
}