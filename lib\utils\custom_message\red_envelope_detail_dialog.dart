import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_cloud_chat_demo/utils/custom_message/red_envelope_message.dart';

class RedEnvelopeDetailDialog extends StatelessWidget {
  final RedEnvelopeMessage redEnvelopeMessage;
  final String messageID;
  final VoidCallback? onOpen;
  final String senderName;
  
  const RedEnvelopeDetailDialog({
    Key? key, 
    required this.redEnvelopeMessage, 
    required this.messageID,
    required this.senderName,
    this.onOpen,
  }) : super(key: key);

  @override
  Widget build(BuildContext context) {
    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    return Dialog(
      backgroundColor: Colors.transparent,
      insetPadding: EdgeInsets.zero,
      child: GestureDetector(
        onTap: () => Navigator.of(context).pop(),
        child: Container(
        width: double.infinity,
        height: double.infinity,
        color: Colors.black.withOpacity(0.5),
        child: GestureDetector(
          onTap:() {},
          child: Center(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // 红包主体
              Container(
                width: 271.w,
                height: 428.h,
                decoration: BoxDecoration(
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Stack(
                  children: [
                    // 背景图片
                    Positioned.fill(
                      child: ClipRRect(
                        borderRadius: BorderRadius.circular(12),
                        child: CachedNetworkImage(
                          imageUrl: redEnvelopeMessage.image ?? "",
                          fit: BoxFit.cover,
                          placeholder: (context, url) => Container(color: const Color(0xFFFF5252)),
                          errorWidget: (context, url, error) => Container(color: const Color(0xFFFF5252)),
                        ),
                      ),
                    ),
                    
                    // 文字内容
                    Positioned(
                      top: 96.h,
                      left: 0,
                      right: 0,
                      child: Container(
                        padding: const EdgeInsets.symmetric(horizontal: 10, vertical: 0),
                        margin: const EdgeInsets.symmetric(horizontal: 10),
                        child: Column(
                          children: [
                            Text(
                              "$senderName发出的红包",
                              style: TextStyle(
                                color: const Color(0xFFE7A400),
                                fontSize: 16.sp,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                            const SizedBox(height: 8),
                            Text(
                              redEnvelopeMessage.remark ?? "",
                              style: TextStyle(
                                color: const Color(0xFFE7A400),
                                fontSize: 20.sp,
                                fontWeight: FontWeight.w500,
                              ),
                              textAlign: TextAlign.center,
                            ),
                          ],
                        ),
                      ),
                    ),
                    
                    // 开按钮
                    Positioned(
                      bottom: 61,
                      left: 0,
                      right: 0,
                      child: Center(
                        child: InkWell(
                          onTap: onOpen,
                          child: Container(
                            width: 80,
                            height: 80,
                            child: const Center(
                              
                            ),
                          ),
                        ),
                      ),
                    ),
                  ],
                ),
              ),
              
              // 关闭按钮
              const SizedBox(height: 20),
              GestureDetector(
                onTap: () => Navigator.of(context).pop(),
                child: Container(
                  width: 40,
                  height: 40,
                  decoration: const BoxDecoration(
                    shape: BoxShape.circle,
                  ),
                  child: Image.asset('assets/icon_close.png', width: 32, height: 32),
                ),
              ),
            ],
          ),
        ),
        ) 
      ),
     )
    );
  }
}