import 'package:flutter/material.dart';
import 'package:flutter_screenutil/flutter_screenutil.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_sdk/models/v2_tim_message.dart';
import 'package:tencent_cloud_chat_demo/models/getPacketDetail_response.dart';
import 'package:tencent_cloud_chat_demo/utils/user_info_local.dart';
import './red_envelope_record.dart';


class RedPacketCollectionPage extends StatefulWidget {
  final String senderName;
  final String remark;
  final String amount;
  final PacketDetailResponse? packetDetail;
  final bool isOpen;
  final String userId;
  final V2TimMessage? message;
  
  const RedPacketCollectionPage({
    super.key, 
    this.senderName = 'Juanmao', 
    this.remark = '恭喜发财，大吉大利',
    this.amount = '520.00', 
    this.userId = '',
    this.packetDetail,
    this.isOpen = false,
    this.message,
  });
  
  @override
  State<RedPacketCollectionPage> createState() => _RedPacketCollectionPageState();
}

class _RedPacketCollectionPageState extends State<RedPacketCollectionPage> {

  @override
  Widget build(BuildContext context) {

      // 红包总金额 
    String amount = widget.packetDetail?.data?.totalAmount ?? widget.amount;
    // 是否群红包
    bool isGroup = widget.packetDetail?.data?.chatType == 'group';
    // 是否拼手气红包
    bool isLucky = widget.packetDetail?.data?.type == 'lucky';


    final userList = widget.packetDetail?.data?.list ?? [];
    debugPrint('userList: ${widget.packetDetail?.data?.toJson()}');
    // 被抢的红包金额
    double receivedAmount = 0;
    if(userList.isNotEmpty) {
      receivedAmount = userList.map((e) => double.tryParse(e.amount ?? '0') ?? 0).reduce((a, b) => a + b);
    }

    // 自己抢到的金额
    String myReceivedAmount = '';
    if(userList.isNotEmpty){
      for (var element in userList) {
        if (element.userId == widget.userId) {
          myReceivedAmount = element.amount ?? '';
          break;
        }
      }
    }

    ScreenUtil.init(
      context,
      designSize: const Size(375, 812),
      minTextAdapt: true,
    );
    
    return Scaffold(
      backgroundColor: Colors.white,
      extendBodyBehindAppBar: true,
      appBar: AppBar(
        backgroundColor: Colors.transparent,
        elevation: 0,
        leading: Center(
          child: SizedBox(
            width: 24.h,
            height: 24.h,
            child: GestureDetector(
              onTap: () => Navigator.pop(context),
              child: Image.asset(
                'assets/icon_right_back.png',
                color: Colors.white,
                fit: BoxFit.contain,
              ),
            ),
          ),
        ),
        actions: [
          IconButton(
            icon: const Icon(Icons.more_horiz, color: Colors.white),
            onPressed: () {
              Navigator.push(
                context,
                MaterialPageRoute(
                  builder: (context) => const RedEnvelopeRecordPage(),
                ),
              );
            },
          ),
        ],
      ),
      body: Column(
        children: [
          // 顶部红色弧形区域
          ClipPath(
            clipper: CurveClipper(),
            child: Container(
              height: 130.h,
              color: const Color(0xFFFF5252),
            ),
          ),
          
          // 内容区域
          SizedBox(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: 20.w, vertical: 0),
              child: Column(
                children: [
                  SizedBox(height: 32.h),
                  
                  // 发送者信息
                  Text(
                    TIM_t_para("{{option1}}发出的红包","发出的红包${widget.senderName}")(option1: widget.senderName),
                    style: TextStyle(
                      fontSize: 16.sp,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  SizedBox(height: 12.h),
                  
                  // 红包备注
                  Text(
                    widget.remark,
                    style: TextStyle(
                      fontSize: 14.sp,
                      color: const Color(0xFF999999),
                    ),
                  ),
                  SizedBox(height: 32.h),
                  
                  // 金额
                  if(!isGroup)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "₱",
                          style: TextStyle(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFFE7A400),
                          ),
                        ),
                        Text(
                          double.tryParse(amount)?.toStringAsFixed(2) ?? '',
                          style: TextStyle(
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFFE7A400),
                            height: 1,
                          ),
                        ),
                      ],
                    ),
                  if(isGroup && !isLucky)
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      crossAxisAlignment: CrossAxisAlignment.end,
                      children: [
                        Text(
                          "₱",
                          style: TextStyle(
                            fontSize: 24.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFFE7A400),
                          ),
                        ),
                        Text(
                          double.tryParse(myReceivedAmount)?.toStringAsFixed(2) ?? double.tryParse(amount)?.toStringAsFixed(2) ?? '',
                          style: TextStyle(
                            fontSize: 36.sp,
                            fontWeight: FontWeight.w500,
                            color: const Color(0xFFE7A400),
                            height: 1,
                          ),
                        ),
                      ],
                  ),

                  if(isGroup && isLucky)
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    crossAxisAlignment: CrossAxisAlignment.end,
                    children: [
                      Text(
                        "₱",
                        style: TextStyle(
                          fontSize: 24.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFFE7A400),
                        ),
                      ),
                      Text(
                        double.tryParse(myReceivedAmount)?.toStringAsFixed(2) ?? '',
                        style: TextStyle(
                          fontSize: 36.sp,
                          fontWeight: FontWeight.w500,
                          color: const Color(0xFFE7A400),
                          height: 1,
                        ),
                      ),
                    ],
                  ),
                  SizedBox(height: 10.h),
                  
                  // 已存入钱包提示
                  if(widget.isOpen && myReceivedAmount != '')
                  Row(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Text(
                        TIM_t('已存入钱包'),
                        style: TextStyle(
                          fontSize: 14.sp,
                          color: const Color(0xFFD3A425),
                        ),
                      ),
                      Icon(
                        Icons.chevron_right,
                        size: 16.sp,
                        color: const Color(0xFFD3A425),
                      ),
                    ],
                  ),
                ],
              ),
            ),
          ),
          Container(
            margin: EdgeInsets.only(top: 24.h),
            height: 12.h,
            color: const Color(0xFFF4F7F8),
          ),
          if(!isGroup)
          Padding(
            padding: const EdgeInsets.symmetric(horizontal: 16.0),
            child: Container(
              padding: EdgeInsets.symmetric(vertical: 12.h),
              alignment: Alignment.centerLeft,
              decoration: const BoxDecoration(
                border: Border(
                  bottom: BorderSide(
                  color: Color(0xFFE9E9E9),
                  width: 0.5,
                ),
              ) ,
            ),
            child: 
            Text.rich(
              TextSpan(
                children: [
                  TextSpan(
                    text: TIM_t('红包金额'),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                   TextSpan(
                    text: ' ₱${ double.tryParse(widget.packetDetail?.data?.totalAmount ?? '0')?.toStringAsFixed(2) ?? ''}',
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                  TextSpan(
                    text: ' ',
                  ),
                  if(widget.isOpen == false)
                  TextSpan(
                    text: TIM_t('待领取'),
                    style: TextStyle(
                      fontSize: 12.sp,
                      color: const Color(0xFF999999),
                      fontWeight: FontWeight.w400,
                    ),
                  ),
                ],
              ),
            )
           ),
          ),
          if((isGroup && isLucky) || (isGroup && !isLucky))
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16.0),
              child: Container(
                padding: EdgeInsets.symmetric(vertical: 12.h),
                alignment: Alignment.centerLeft,
                decoration: const BoxDecoration(
                  border: Border(
                    bottom: BorderSide(
                      color: Color(0xFFE9E9E9),
                      width: 0.5,
                    ),
                  ),
                ),
                child: Text.rich(
                  TextSpan(
                    children: [
                      TextSpan(
                        text: '${widget.packetDetail?.data?.totalCount ?? 0} ',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF999999),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      TextSpan(
                        text: TIM_t('个红包'),
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF999999),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      TextSpan(
                        text: ', ',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF999999),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                      TextSpan(
                        text: userList.length == widget.packetDetail?.data?.totalCount ? TIM_t('全部被抢光') : '₱${receivedAmount.toStringAsFixed(2)}/${double.tryParse(amount)?.toStringAsFixed(2) ?? ''}',
                        style: TextStyle(
                          fontSize: 12.sp,
                          color: const Color(0xFF999999),
                          fontWeight: FontWeight.w400,
                        ),
                      ),
                    ],
                  ),
                ),
              ),
            ),

            SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: userList.asMap().entries.map((entry) {
                  return _redEnvelopeItem(entry.value, entry.key);
                }).toList(),
              ),
            )
        ],
      ),
    );
  }

    Widget _redEnvelopeItem(item, int index) {
    return Container(
        padding: const EdgeInsets.symmetric(vertical: 12, horizontal: 16),
        decoration: BoxDecoration(
            color: const Color(0xFFFFFFFF),
            borderRadius: BorderRadius.circular(5),
            border: const Border(
              bottom: BorderSide(
                color: Color(0xFFE9E9E9),
                width: 0.5,
              ),
            )),
        child: Row(
          mainAxisAlignment: MainAxisAlignment.spaceBetween,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  item.nick!,
                  style: TextStyle(
                      color: const Color(0xFF333333),
                      fontSize: 14.sp,
                      fontWeight: FontWeight.w500),
                ),
                Text(
                item.snatchTime,
                  style: TextStyle(
                      color: const Color(0xFF999999),
                      fontSize: 12.sp,
                      fontWeight: FontWeight.w400),
                )
              ],
            ),
            Text(
             '₱'+ item.amount,
              style: TextStyle(
                  color: const Color(0xFF333333),
                  fontSize: 14.sp,
                  fontWeight: FontWeight.w500),
            ),
          ],
        ));
  }
}

class CurveClipper extends CustomClipper<Path> {
  @override
  Path getClip(Size size) {
    final path = Path();
    path.lineTo(0, size.height * 0.70); // 左侧点
    
    // 使用三次贝塞尔曲线创建更平滑的弧形
    path.cubicTo(
      size.width * 0.25, // 第一控制点x
      size.height * 1.0, // 第一控制点y
      size.width * 0.75, // 第二控制点x
      size.height * 1.0, // 第二控制点y
      size.width, // 终点x
      size.height * 0.70, // 终点y
    );
    
    path.lineTo(size.width, 0); // 回到右上角
    return path;
  }

  @override
  bool shouldReclip(CustomClipper<Path> oldClipper) => false;
}