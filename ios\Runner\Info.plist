<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE plist PUBLIC "-//Apple//DTD PLIST 1.0//EN" "http://www.apple.com/DTDs/PropertyList-1.0.dtd">
<plist version="1.0">
<dict>
	<!-- Google Sign-in Section -->
	<key>CFBundleURLTypes</key>
	<array>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<!-- TODO Replace this value: -->
				<!-- Copied from GoogleService-Info.plist key REVERSED_CLIENT_ID -->
				<string>com.googleusercontent.apps.545427402667-v1r3qc8o9r69jm5f4oaoqebc79lo47u7</string>
			</array>
		</dict>
		<dict>
			<key>CFBundleTypeRole</key>
			<string>Editor</string>
			<key>CFBundleURLSchemes</key>
			<array>
				<string>uikitflutterdemo</string>
			</array>
		</dict>
	</array>
	<!-- End of the Google Sign-in Section -->
	<key>GIDClientID</key>
	<!-- TODO Replace this value: -->
	<!-- Copied from GoogleService-Info.plist key CLIENT_ID -->
	<string>545427402667-v1r3qc8o9r69jm5f4oaoqebc79lo47u7.apps.googleusercontent.com</string>
	<key>CADisableMinimumFrameDurationOnPhone</key>
	<true/>
	<key>CFBundleAllowMixedLocalizations</key>
	<true/>
	<key>CFBundleLocalizations</key>
	<array>
		<string>zh_TW</string>
		<string>en</string>
		<string>zh_CN</string>
	</array>
	<key>CFBundleSignature</key>
	<string>????</string>
	<key>LSApplicationQueriesSchemes</key>
	<array>
		<string>weixin</string>
		<string>weixinULAPI</string>
	</array>
	<key>NSAppTransportSecurity</key>
	<dict>
		<key>NSAllowsArbitraryLoads</key>
		<false/>
		<key>NSExceptionDomains</key>
		<dict>
			<key>************</key>
			<string></string>
		</dict>
	</dict>
	<key>UIBackgroundModes</key>
	<array>
		<string>audio</string>
	</array>
	<key>UIViewControllerBasedStatusBarAppearance</key>
	<false/>
	<key>flutter_apns.disable_firebase_core</key>
	<false/>
	<key>io.flutter.embedded_views_preview</key>
	<string>YES</string>
	<!-- Camera and Photo Library permissions -->
	<key>NSCameraUsageDescription</key>
	<string>需要访问相机来拍摄照片和录制视频</string>
	<key>NSMicrophoneUsageDescription</key>
	<string>需要访问麦克风来录制视频</string>
	<key>NSPhotoLibraryUsageDescription</key>
	<string>需要访问相册来选择照片和视频</string>
</dict>
</plist>
