import 'package:flutter/material.dart';
import 'package:tencent_chat_i18n_tool/tencent_chat_i18n_tool.dart';
import 'package:tencent_cloud_chat_demo/src/widgets/bottom_sheet_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'package:file_picker/file_picker.dart';
import 'dart:io';
import 'dart:typed_data';
import '../../../../apis/tuils_api.dart';
import './permission_type.dart';
import '../../../../utils/toast.dart';
import 'package:dio/dio.dart';
import '../../../../models/upload_response.dart';
import 'custom_camera_page.dart';
import '../../../utils/video_thumbnail_helper.dart';
import '../../../widgets/media_preview_page.dart';

class MomentsPublishPage extends StatefulWidget {
  final List<XFile>? initialImages;

  const MomentsPublishPage({super.key, this.initialImages});

  @override
  State<StatefulWidget> createState() => _MomentsPublishPageState();
}

class _MomentsPublishPageState extends State<MomentsPublishPage> {
  TextEditingController _controller = TextEditingController();
  bool _isPublishing = false; // 添加发布状态变量
  String permissionType = 'public';
  List<String> visibleUsers = [];
  List<Map<String, String>> visibleUsersInfo = [];
  List<String> invisibleUsers = [];
  List<Map<String, String>> invisibleUsersInfo = [];
  final ImagePicker _picker = ImagePicker();
  List<XFile> _imageFiles = [];

  // 视频缩略图缓存
  final Map<String, Uint8List?> _thumbnailCache = {};

  @override
  void initState() {
    super.initState();
    // 如果有初始图片，添加到图片列表中
    if (widget.initialImages != null && widget.initialImages!.isNotEmpty) {
      _imageFiles.addAll(widget.initialImages!);
    }
  }

  @override
  void dispose() {
    _controller.dispose();
    // 清理缩略图缓存
    _thumbnailCache.clear();
    super.dispose();
  }

  // 谁可以看映射
  Map<String, String> permissionTypeMap = {
    'public': TIM_t('公开'),
    'private': TIM_t('私密'),
    'custom_visible': TIM_t('部分可见'),
    'custom_invisible': TIM_t('部分不可见'),
  };

  // 跳转谁可以看
  void _navigateToPermissionType() {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => PermissionTypePage(
          initialPermissionType: permissionType,
          initialVisibleFriendList: visibleUsers,
          initialVisibleFriendInfoList: visibleUsersInfo,
          initialInvisibleFriendList: invisibleUsers,
          initialInvisibleFriendInfoList: invisibleUsersInfo,
        ),
      ),
    ).then((value) {
      if (value != null) {
        debugPrint('权限设置返回值: $value');
        if (value['permissionType'] == 'custom_visible') {
          setState(() {
            permissionType = value['permissionType'];
            // 修正键名，与permission_type.dart中返回的键名一致
            visibleUsers = value['visibleFriendList'] ?? [];
            visibleUsersInfo = value['visibleFriendInfoList'] ?? [];
          });
        }
        if (value['permissionType'] == 'custom_invisible') {
          setState(() {
            permissionType = value['permissionType'];
            // 修正键名，与permission_type.dart中返回的键名一致
            invisibleUsers = value['invisibleFriendList'] ?? [];
            invisibleUsersInfo = value['invisibleFriendInfoList'] ?? [];
          });
        }
        if (value['permissionType'] == 'public' ||
            value['permissionType'] == 'private') {
          setState(() {
            permissionType = value['permissionType'];
            visibleUsers = [];
            visibleUsersInfo = [];
            invisibleUsers = [];
            invisibleUsersInfo = [];
          });
        }

        debugPrint('更新后的权限类型: $permissionType');
        debugPrint('更新后的可见用户: $visibleUsers');
        debugPrint('更新后的不可见用户: $invisibleUsers');
      }
    });
  }

  // 打开自定义相机
  Future<void> _openCustomCamera() async {
    try {
      // 检查是否已达到最大文件数量限制
      if (_imageFiles.length >= 9) {
        ToastUtils.toast(TIM_t("最多只能选择9个媒体文件"));
        return;
      }

      final result = await Navigator.push(
        context,
        MaterialPageRoute(
          builder: (context) => const CustomCameraPage(),
        ),
      );

      if (result != null && result is Map) {
        final String type = result['type'];
        final XFile file = result['file'];

        if (type == 'image' || type == 'video') {
          setState(() {
            _imageFiles.add(file);
          });
        }
      }
    } catch (e) {
      print("打开自定义相机出错: $e");
      ToastUtils.toast(TIM_t("相机启动失败"));
    }
  }

  // 从相册选择媒体文件（图片和视频）
  Future<void> _pickMediaFromGallery() async {
    try {
      // 计算还能选择多少个文件
      int remainingSlots = 9 - _imageFiles.length;
      if (remainingSlots <= 0) {
        ToastUtils.toast(TIM_t("最多只能选择9个媒体文件"));
        return;
      }

      FilePickerResult? result = await FilePicker.platform.pickFiles(
        type: FileType.media, // 选择媒体文件（图片和视频）
        allowMultiple: true, // 允许多选
        allowCompression: true, // 允许压缩
      );

      if (result != null && result.files.isNotEmpty) {
        // 将 PlatformFile 转换为 XFile
        List<XFile> selectedFiles = result.files.map((file) {
          return XFile(file.path!);
        }).toList();

        // 检查选择的文件数量是否会超过限制
        if (selectedFiles.length > remainingSlots) {
          ToastUtils.toast(TIM_t("最多只能选择${remainingSlots}个媒体文件，已为您选择前${remainingSlots}个"));
          selectedFiles = selectedFiles.take(remainingSlots).toList();
        }

        debugPrint("选择媒体文件成功: ${selectedFiles.toString()}");
        setState(() {
          _imageFiles.addAll(selectedFiles);
        });
      }
    } catch (e) {
      print("选择媒体文件出错: $e");
    }
  }

  // 删除图片
  void _removeImage(int index) {
    final file = _imageFiles[index];
    // 如果是视频文件，清理缓存
    if (VideoThumbnailHelper.isVideoFile(file.path)) {
      _thumbnailCache.remove(file.path);
    }
    setState(() {
      _imageFiles.removeAt(index);
    });
  }

  // 获取或生成视频缩略图
  Future<Uint8List?> _getVideoThumbnail(String videoPath) async {
    // 检查缓存
    if (_thumbnailCache.containsKey(videoPath)) {
      return _thumbnailCache[videoPath];
    }

    // 生成缩略图并缓存
    final thumbnail = await VideoThumbnailHelper.generateThumbnail(videoPath);
    _thumbnailCache[videoPath] = thumbnail;
    return thumbnail;
  }

  // 构建视频缩略图Widget
  Widget _buildVideoThumbnailWidget(XFile videoFile) {
    return Container(
      width: 80,
      height: 80,
      margin: const EdgeInsets.only(right: 8, bottom: 8),
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(4),
        color: Colors.grey[300],
      ),
      child: FutureBuilder<Uint8List?>(
        future: _getVideoThumbnail(videoFile.path),
        builder: (context, snapshot) {
          if (snapshot.connectionState == ConnectionState.waiting) {
            // 加载中显示占位符
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.grey[300],
              ),
              child: const Stack(
                children: [
                  Center(
                    child: CircularProgressIndicator(
                      strokeWidth: 2,
                      valueColor: AlwaysStoppedAnimation<Color>(Colors.grey),
                    ),
                  ),
                ],
              ),
            );
          } else if (snapshot.hasData && snapshot.data != null) {
            // 显示生成的缩略图
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                image: DecorationImage(
                  image: MemoryImage(snapshot.data!),
                  fit: BoxFit.cover,
                ),
              ),
              child: Stack(
                children: [
                  // 视频遮罩
                  Container(
                    decoration: BoxDecoration(
                      borderRadius: BorderRadius.circular(4),
                      color: Colors.black26,
                    ),
                  ),
                  // 播放图标
                  const Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            );
          } else {
            // 缩略图生成失败，显示默认视频图标
            return Container(
              decoration: BoxDecoration(
                borderRadius: BorderRadius.circular(4),
                color: Colors.grey[400],
              ),
              child: const Stack(
                children: [
                  Center(
                    child: Icon(
                      Icons.videocam,
                      color: Colors.white,
                      size: 32,
                    ),
                  ),
                  Center(
                    child: Icon(
                      Icons.play_circle_filled,
                      color: Colors.white,
                      size: 24,
                    ),
                  ),
                ],
              ),
            );
          }
        },
      ),
    );
  }

  // 预览媒体文件
  void _previewMedia(int index) {
    Navigator.push(
      context,
      MaterialPageRoute(
        builder: (context) => MediaPreviewPage(
          mediaFiles: _imageFiles,
          initialIndex: index,
        ),
      ),
    );
  }

  // 上传媒体文件（图片或视频）
  Future<List<Map<String, Object>>> _uploadImages() async {
    List<Map<String, Object>> uploadedUrls = [];

    try {
      for (XFile file in _imageFiles) {
        // 判断文件类型
        String mediaType = "image";
        String fileName = file.name.toLowerCase();
        if (fileName.endsWith('.mp4') || fileName.endsWith('.mov') ||
            fileName.endsWith('.avi') || fileName.endsWith('.mkv') ||
            fileName.endsWith('.3gp') || fileName.endsWith('.webm')) {
          mediaType = "video";
        }

        // 创建FormData对象
        FormData formData = FormData.fromMap({
          "file": await MultipartFile.fromFile(
            file.path,
            filename: file.name,
          ),
          "folder": 6
        });
        debugPrint("上传${mediaType}结果: ${formData.toString()}");
        // 调用上传接口
        var result = await Api.instance.upload(formData);
        debugPrint("上传${mediaType}结果: ${result.toString()}");
        if (result is UploadResponse &&
            result.code == 0 &&
            result.data != null) {
          String? url = result.data?.fileUrl; // 修改这里，使用fileUrl而不是url
          if (url != null && url.isNotEmpty) {
            var obj = {
              "mediaType": mediaType,
              "mediaUrl": url,
              "sortOrder": 0,
            };
            uploadedUrls.add(obj);
          }
        }
      }

      return uploadedUrls;
    } catch (e) {
      debugPrint("上传媒体文件失败: $e");
      ToastUtils.toast(TIM_t("上传媒体文件失败"));
      return [];
    }
  }

// 发布朋友圈
  Future<void> _publishMoment() async {
    // 开启loading
    if (_isPublishing) return;
    // 开启loading
    setState(() {
      _isPublishing = true;
    });
    // 显示加载指示器
    ToastUtils.showLoading(message: TIM_t("发布中"));

    try {
      if (_imageFiles.isNotEmpty) {
        // 先上传图片
        List<Map<String, Object>> imageUrls = await _uploadImages();
        if (imageUrls.isEmpty) {
          ToastUtils.toast(TIM_t("上传图片失败"));
          return; // 如果图片上传失败，则不继续发布
        }
        // 发布朋友圈，包含图片URL
        var data = {
          "content": _controller.text,
          "momentMediaList": imageUrls,
          "permissionType": permissionType,
        };

        if (data['permissionType'] == 'custom_visible') {
          data['visibleFriendList'] = _getUserIds(visibleUsers);
        }
        if (data['permissionType'] == 'custom_invisible') {
          data['invisibleFriendList'] = _getUserIds(invisibleUsers);
        }

        var res = await Api.instance.publish(data);
        if (res.code == 0 && res.ok!) {
          Navigator.pop(context, {"success": true});
        }
      } else {
        if (_controller.text.isEmpty) {
          ToastUtils.toast(TIM_t("请输入内容"));
          return;
        }
        // 无图片发布
        Map<String, dynamic> data = {
          "content": _controller.text,
          "permissionType": permissionType,
        };
        if (data['permissionType'] == 'custom_visible') {
          data['visibleFriendList'] = _getUserIds(visibleUsers);
        }
        if (data['permissionType'] == 'custom_invisible') {
          data['invisibleFriendList'] = _getUserIds(invisibleUsers);
        }
        debugPrint("发布朋友圈数据: ${data.toString()}");
        var res = await Api.instance.publish(data);
        if (res.code == 0 && res.ok!) {
          Navigator.pop(context, {"success": true});
        }
      }
    } catch (e) {
      ToastUtils.toast(TIM_t("发布失败"));
    } finally {
      setState(() {
        _isPublishing = false;
      });
      ToastUtils.hideLoading();
    }
  }

// 处理 List<String> 类型的输入
  List<String> _getUserIds(List<String> userIds) {
    return userIds; // 直接返回，因为已经是正确的格式
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        resizeToAvoidBottomInset:  false,
        backgroundColor: Colors.white,
        appBar: AppBar(
          backgroundColor: Colors.white,
          title: Text(TIM_t('发布动态')),
          automaticallyImplyLeading: false,
          // 禁用自动生成的返回按钮
          leading: null,
          // 移除leading
          leadingWidth: 0,
          // 设置leading宽度为0
          actions: [
            // 在actions中添加自定义宽度的取消按钮
            Padding(
              padding: const EdgeInsets.only(left: 16),
              child: GestureDetector(
                onTap: () {
                  Navigator.pop(context);
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  margin: const EdgeInsets.only(right: 16),
                  height: 32,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xFFE9E9E9),
                  ),
                  child: Center(
                    child: Text(TIM_t('取消'),
                        style: const TextStyle(color: Color(0XFF666666))),
                  ),
                ),
              ),
            ),
            const Spacer(), // 添加一个Spacer将发布按钮推到右边
            // 添加发布按钮
            Padding(
              padding: const EdgeInsets.only(right: 16),
              child: GestureDetector(
                onTap: () {
                  _isPublishing ? null : _publishMoment();
                  // 处理发布逻辑
                },
                child: Container(
                  padding:
                      const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
                  height: 32,
                  decoration: BoxDecoration(
                    borderRadius: BorderRadius.circular(8),
                    color: const Color(0xFF0072FC),
                  ),
                  child: Center(
                    child: Text(TIM_t("发布"),
                        style: const TextStyle(color: Colors.white)),
                  ),
                ),
              ),
            ),
          ],
        ),
        body: Padding(
          padding: const EdgeInsets.symmetric(horizontal: 20),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              _editBox(),
              _uploadBox(),
              _locationBox(),
              _whoCanSeeBox()
            ],
          ),
        ));
  }

  // 输入框
  Widget _editBox() {
    return Container(
      margin: const EdgeInsets.only(top: 10, bottom: 10),
      // height: 150,
      child: TextField(
          minLines: 1,
          maxLines: 6,
          controller: _controller,
          keyboardType: TextInputType.multiline,
          autofocus: true,
          style: const TextStyle(fontSize: 13),
          decoration: InputDecoration(
              contentPadding:
                  const EdgeInsets.symmetric(horizontal: 10, vertical: 10),
              border: OutlineInputBorder(
                  borderRadius: BorderRadius.circular(5.0),
                  borderSide: const BorderSide(
                    color: Colors.transparent,
                  )),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(5.0),
                borderSide: const BorderSide(
                  color: Colors.transparent,
                ),
              ),
              focusedBorder: OutlineInputBorder(
                //选中时外边框颜色
                borderRadius: BorderRadius.circular(5.0),
                borderSide: const BorderSide(
                  color: Colors.transparent,
                ),
              ),
              hintStyle: const TextStyle(
                color: Color(0xFFAEA4A3),
              ),
              hintText: TIM_t('这一刻的想法') + '...')),
    );
  }

  // 上传组件
  Widget _uploadBox() {
    return Container(
      margin: const EdgeInsets.only(top: 16,left: 10),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 图片网格
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // 已选择的图片
              ..._imageFiles.asMap().entries.map((entry) {
                int index = entry.key;
                XFile file = entry.value;
                bool isVideo = VideoThumbnailHelper.isVideoFile(file.path);

                return Stack(
                  children: [
                    GestureDetector(
                      onTap: () => _previewMedia(index),
                      child: isVideo
                        ? _buildVideoThumbnailWidget(file)
                        : Container(
                            width: 80,
                            height: 80,
                            margin: const EdgeInsets.only(right: 8, bottom: 8),
                            decoration: BoxDecoration(
                              borderRadius: BorderRadius.circular(4),
                              image: DecorationImage(
                                image: FileImage(File(file.path)),
                                fit: BoxFit.cover,
                              ),
                            ),
                          ),
                    ),
                    Positioned(
                      right: 0,
                      top: 0,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.black45,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
              // 添加图片按钮（如果图片数量小于9张）
              if (_imageFiles.length < 9)
                GestureDetector(
                  onTap: () {
                    _showImagePickerOptions();
                  },
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF4F4F4),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.add,
                        color: Color(0xFFAAAAAA),
                        size: 24,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // 显示媒体选择选项
  void _showImagePickerOptions() {
    BottomSheetPicker.show(
      context: context,
      title: "",
      options: [
        BottomSheetOption(
          title: TIM_t("拍摄"),
          onTap: () {
            _openCustomCamera();
          },
        ),
        BottomSheetOption(
          title: TIM_t("从手机相册选择"),
          onTap: () {
            _pickMediaFromGallery();
          },
        )
      ],
      cancelText: "取消",
    );
  }

  // 所在位置
  Widget _locationBox() {
    return Column(
      children: [
        SizedBox(
          height: 56,
        ),
        InkWell(
            onTap: () {
              ToastUtils.toast('开发中');
            },
            child: Container(
              padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),

              decoration: BoxDecoration(
                  border: Border(
                top: const BorderSide(color: Color(0xFFE9E9E9), width: 1.0),
              )),
              child: Row(
                children: [
                  Image.asset('assets/moments/icon_location.png',
                      width: 20, height: 20),
                  const SizedBox(width: 12),
                  Text(TIM_t("所在位置"),
                      style: const TextStyle(
                          color: Color(0xFF333333), fontSize: 14)),
                  const Spacer(),
                  Image.asset('assets/moments/icon_right.png',
                      width: 16, height: 16),
                ],
              ),
            ))
      ],
    );
  }

  // 谁可以看
  Widget _whoCanSeeBox() {
    return InkWell(
        onTap: () {
          // 跳转谁看
          _navigateToPermissionType();
        },
        child: Container(
          padding: const EdgeInsets.symmetric(vertical: 16, horizontal: 12),
          decoration: BoxDecoration(
              border: Border(
            top: const BorderSide(color: Color(0xFFE9E9E9), width: 1.0),
          )),
          child: Row(
            children: [
              Image.asset('assets/moments/icon_who_can_see.png',
                  width: 20, height: 20),
              const SizedBox(width: 12),
              Text(TIM_t("谁可以看"),
                  style:
                      const TextStyle(color: Color(0xFF333333), fontSize: 14)),
              const Spacer(),
              Text(permissionTypeMap[permissionType] ?? "公开"),
              const SizedBox(width: 12),
              Image.asset('assets/moments/icon_right.png',
                  width: 16, height: 16),
            ],
          ),
        ));
  }
}
