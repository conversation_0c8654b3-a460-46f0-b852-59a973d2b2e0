import 'package:flutter/material.dart';
import 'package:tencent_cloud_chat_demo/src/widgets/bottom_sheet_picker.dart';
import 'package:image_picker/image_picker.dart';
import 'dart:io';
import '../../../../../apis/tuils_api.dart';
import '../permission_type.dart';
import '../../../../../utils/toast.dart';
import 'package:dio/dio.dart';
import '../../../../../models/upload_response.dart';
import 'dart:io';
import 'package:path_provider/path_provider.dart';

class ChooseCoverPage extends StatefulWidget {
  const ChooseCoverPage({super.key});
  @override
  State<StatefulWidget> createState() => _ChooseCoverPageState();
}

class _ChooseCoverPageState extends State<ChooseCoverPage> {
  TextEditingController _controller = TextEditingController();
  bool _isPublishing = false; // 添加发布状态变量
  String permissionType = 'public';
  List<String> visibleUsers = [];
  List<Map<String, String>> visibleUsersInfo = [];
  List<String> invisibleUsers = [];
  List<Map<String, String>> invisibleUsersInfo = [];
  final ImagePicker _picker = ImagePicker();
  List<XFile> _imageFiles = [];
  @override
  void initState() {
    // TODO: implement initState
    super.initState();
  }

  // 拍照
  Future<void> _takePhoto() async {
    try {
      final XFile? photo = await _picker.pickImage(source: ImageSource.camera, imageQuality: 10);
      if (photo != null) {
        debugPrint("选择拍照图片成功: ${photo.toString()}");
        setState(() {
          _imageFiles.add(photo);
        });
        // 上传图片
        List<Map<String, Object>> imageUrls = await _uploadImages();
        debugPrint("上传拍照图片结果: ${imageUrls.toString()}");
        if (imageUrls.isEmpty) {
          ToastUtils.toast("上传图片失败, 请重试");
          return; // 如果图片上传失败，则不继续发布
        }
        // 发布朋友圈，包含图片URL
        var data = {
          "momentCoverUrl": imageUrls[0]['mediaUrl'],
        };
        var res = await Api.instance.updateCover(data);
        if (res.code == 0 && res.ok!) {
          ToastUtils.toast("修改封面成功");
          Navigator.pop(context);
        }
      }
    } catch (e) {
      print("拍照出错: $e");
    }
  }

  // 从相册选择
  Future<void> _pickImage() async {
    try {
      final List<XFile>? images = await _picker.pickMultiImage();
      if (images != null && images.isNotEmpty) {
        debugPrint("选择图片成功: ${images.toString()}");
        setState(() {
          _imageFiles.addAll(images);
        });
        // 上传图片
        List<Map<String, Object>> imageUrls = await _uploadImages();
        debugPrint("上传图片结果111111: ${imageUrls.toString()}");
        if (imageUrls.isEmpty) {
          ToastUtils.toast("上传图片失败, 请重试");
          return; // 如果图片上传失败，则不继续发布
        }
        // 发布朋友圈，包含图片URL
        var data = {
          "momentCoverUrl": imageUrls[0]['mediaUrl'],
        };
        var res = await Api.instance.updateCover(data);
        if (res.code == 0 && res.ok!) {
          ToastUtils.toast("修改封面成功");
          Navigator.pop(context);
        }
      }
    } catch (e) {
      ToastUtils.toast("选择图片出错");
    }
  }

  // 删除图片
  void _removeImage(int index) {
    setState(() {
      _imageFiles.removeAt(index);
    });
  }

  // 上传图片
  Future<List<Map<String, Object>>> _uploadImages() async {
    List<Map<String, Object>> uploadedUrls = [];

    try {
      for (XFile image in _imageFiles) {
        // 创建FormData对象
        FormData formData = FormData.fromMap({
          "file": await MultipartFile.fromFile(
            image.path,
            filename: image.name,
          ),
          "folder": 1
        });
        debugPrint("上传图片结果: ${formData.toString()}");
        // 调用上传接口
        var result = await Api.instance.upload(formData);
        debugPrint("上传图片结果: ${result.toString()}");
        if (result is UploadResponse &&
            result.code == 0 &&
            result.data != null) {
          String? url = result.data?.fileUrl; // 修改这里，使用fileUrl而不是url
          if (url != null && url.isNotEmpty) {
            var obj = {
              "mediaType": "image",
              "mediaUrl": url,
              "sortOrder": 0,
            };
            uploadedUrls.add(obj);
          }
        }
      }

      return uploadedUrls;
    } catch (e) {
      debugPrint("上传图片失败: $e");
      ToastUtils.toast("上传图片失败");
      return [];
    }
  }

// 发布朋友圈
  Future<void> _publishMoment() async {
    // 开启loading
    if (_isPublishing) return;
    // 开启loading
    setState(() {
      _isPublishing = true;
    });

    try {
      if (_imageFiles.isNotEmpty) {
        // 先上传图片
        List<Map<String, Object>> imageUrls = await _uploadImages();
        if (imageUrls.isEmpty) {
          ToastUtils.toast("上传图片失败, 请重试");
          return; // 如果图片上传失败，则不继续发布
        }
        // 发布朋友圈，包含图片URL
        var data = {
          "content": _controller.text,
          "momentMediaList": imageUrls,
          "permissionType": permissionType,
        };

        if (data['permissionType'] == 'custom_visible') {
          data['visibleFriendList'] = _getUserIds(visibleUsers);
        }
        if (data['permissionType'] == 'custom_invisible') {
          data['invisibleFriendList'] = _getUserIds(invisibleUsers);
        }

        var res = await Api.instance.publish(data);
        if (res.code == 0 && res.ok!) {
          ToastUtils.toast("发布成功");
          Navigator.pop(context);
        }
      } else {
        if (_controller.text.isEmpty) {
          ToastUtils.toast("请输入内容");
          return;
        }
        // 无图片发布
        Map<String, dynamic> data = {
          "content": _controller.text,
          "permissionType": permissionType,
        };
        if (data['permissionType'] == 'custom_visible') {
          data['visibleFriendList'] = _getUserIds(visibleUsers);
        }
        if (data['permissionType'] == 'custom_invisible') {
          data['invisibleFriendList'] = _getUserIds(invisibleUsers);
        }
        debugPrint("发布朋友圈数据: ${data.toString()}");
        var res = await Api.instance.publish(data);
        if (res.code == 0 && res.ok!) {
          ToastUtils.toast("发布成功");
          Navigator.pop(context);
        }
      }
    } catch (e) {
      ToastUtils.toast("发布失败");
    } finally {
      setState(() {
        _isPublishing = false;
      });
    }
  }

// 处理 List<String> 类型的输入
  List<String> _getUserIds(List<String> userIds) {
    return userIds; // 直接返回，因为已经是正确的格式
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
        appBar: AppBar(
          title: const Text('更换朋友圈封面', style: TextStyle(fontSize: 16)),
          leading: Padding(
            padding: const EdgeInsets.only(left: 1),
            child: GestureDetector(
              onTap: () {
                Navigator.pop(context);
              },
              child: Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                margin: const EdgeInsets.symmetric(vertical: 8),
                child: Image.asset('assets/moments/icon_back_b.png', width: 16, height: 16,)
              ),
            ),
           
          ),
           backgroundColor: Colors.white,
        ),
        body: Container(
          color: const Color(0XFFF6F6F6),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                color: Colors.white,
                child: InkWell(
                    onTap: () => {
                      _pickImage()
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0XFFE9E9E9),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Text('从手机相册选择',
                              style: TextStyle(
                                  fontSize: 14, color: Color(0XFF333333))),
                          const Spacer(),
                          Image.asset(
                            'assets/moments/icon_right.png',
                            width: 16,
                            height: 16,
                          )
                        ],
                      ),
                    )),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                color: Colors.white,
                child: InkWell(
                    onTap: () => {
                      _takePhoto()
                    },
                    child: Container(
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      decoration: const BoxDecoration(
                        color: Colors.white,
                        border: Border(
                          bottom: BorderSide(
                            color: Color(0XFFE9E9E9),
                            width: 1,
                          ),
                        ),
                      ),
                      child: Row(
                        children: [
                          const Text('拍一个',
                              style: TextStyle(
                                  fontSize: 14, color: Color(0XFF333333))),
                          const Spacer(),
                          Image.asset(
                            'assets/moments/icon_right.png',
                            width: 16,
                            height: 16,
                          )
                        ],
                      ),
                    )),
              )
            ],
          ),
        ));
  }

  // 上传组件
  Widget _uploadBox() {
    return Container(
      margin: const EdgeInsets.only(top: 16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisAlignment: MainAxisAlignment.start,
        children: [
          // 图片网格
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: [
              // 已选择的图片
              ..._imageFiles.asMap().entries.map((entry) {
                int index = entry.key;
                XFile file = entry.value;
                return Stack(
                  children: [
                    Container(
                      width: 80,
                      height: 80,
                      margin: const EdgeInsets.only(right: 8, bottom: 8),
                      decoration: BoxDecoration(
                        borderRadius: BorderRadius.circular(4),
                        image: DecorationImage(
                          image: FileImage(File(file.path)),
                          fit: BoxFit.cover,
                        ),
                      ),
                    ),
                    Positioned(
                      right: 0,
                      top: 0,
                      child: GestureDetector(
                        onTap: () => _removeImage(index),
                        child: Container(
                          padding: const EdgeInsets.all(2),
                          decoration: const BoxDecoration(
                            color: Colors.black45,
                            shape: BoxShape.circle,
                          ),
                          child: const Icon(
                            Icons.close,
                            size: 16,
                            color: Colors.white,
                          ),
                        ),
                      ),
                    ),
                  ],
                );
              }).toList(),
              // 添加图片按钮（如果图片数量小于9张）
              if (_imageFiles.length < 9)
                GestureDetector(
                  onTap: () {
                    _showImagePickerOptions();
                  },
                  child: Container(
                    width: 80,
                    height: 80,
                    decoration: BoxDecoration(
                      color: const Color(0xFFF4F4F4),
                      borderRadius: BorderRadius.circular(4),
                    ),
                    child: const Center(
                      child: Icon(
                        Icons.add,
                        color: Color(0xFFAAAAAA),
                        size: 24,
                      ),
                    ),
                  ),
                ),
            ],
          ),
        ],
      ),
    );
  }

  // 显示图片选择选项
  void _showImagePickerOptions() {
    BottomSheetPicker.show(
      context: context,
      title: "",
      options: [
        BottomSheetOption(
          title: "拍摄",
          onTap: () {
            _takePhoto();
          },
        ),
        BottomSheetOption(
          title: "从手机相册选择",
          onTap: () {
            _pickImage();
          },
        )
      ],
      cancelText: "取消",
    );
  }
}
