PODS:
  - audio_session (0.0.1):
    - Flutter
  - better_player_plus (1.0.0):
    - <PERSON><PERSON> (~> 6.0.0)
    - Flutter
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - PINCache
  - Cache (6.0.0)
  - device_info_plus (0.0.1):
    - Flutter
  - DKImagePickerController/Core (4.3.9):
    - DKImagePickerController/ImageDataManager
    - DKImagePickerController/Resource
  - DKImagePickerController/ImageDataManager (4.3.9)
  - DKImagePickerController/PhotoGallery (4.3.9):
    - DKImagePickerController/Core
    - DKPhotoGallery
  - DKImagePickerController/Resource (4.3.9)
  - DKPhotoGallery (0.0.19):
    - DKPhotoGallery/Core (= 0.0.19)
    - DKPhotoGallery/Model (= 0.0.19)
    - DKPhotoGallery/Preview (= 0.0.19)
    - DKPhotoGallery/Resource (= 0.0.19)
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Core (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Preview
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Model (0.0.19):
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Preview (0.0.19):
    - DKPhotoGallery/Model
    - DKPhotoGallery/Resource
    - SDWebImage
    - SwiftyGif
  - DKPhotoGallery/Resource (0.0.19):
    - SDWebImage
    - SwiftyGif
  - fc_native_video_thumbnail (0.0.1):
    - Flutter
    - FlutterMacOS
  - file_picker (0.0.1):
    - DKImagePickerController/PhotoGallery
    - Flutter
  - Flutter (1.0.0)
  - flutter_image_compress_common (1.0.0):
    - Flutter
    - Mantle
    - SDWebImage
    - SDWebImageWebPCoder
  - flutter_native_splash (2.4.3):
    - Flutter
  - flutter_plugin_record_plus (0.0.1):
    - Flutter
  - fluttertoast (0.0.2):
    - Flutter
    - Toast
  - GCDWebServer (3.5.4):
    - GCDWebServer/Core (= 3.5.4)
  - GCDWebServer/Core (3.5.4)
  - HLSCachingReverseProxyServer (0.1.0):
    - GCDWebServer (~> 3.5)
    - PINCache (>= 3.0.1-beta.3)
  - HydraAsync (2.0.6)
  - image_cropper (0.0.4):
    - Flutter
    - TOCropViewController (~> 2.7.4)
  - image_gallery_saver_plus (0.0.1):
    - Flutter
  - image_picker_ios (0.0.1):
    - Flutter
  - just_audio (0.0.1):
    - Flutter
    - FlutterMacOS
  - libwebp (1.5.0):
    - libwebp/demux (= 1.5.0)
    - libwebp/mux (= 1.5.0)
    - libwebp/sharpyuv (= 1.5.0)
    - libwebp/webp (= 1.5.0)
  - libwebp/demux (1.5.0):
    - libwebp/webp
  - libwebp/mux (1.5.0):
    - libwebp/demux
  - libwebp/sharpyuv (1.5.0)
  - libwebp/webp (1.5.0):
    - libwebp/sharpyuv
  - Mantle (2.2.0):
    - Mantle/extobjc (= 2.2.0)
  - Mantle/extobjc (2.2.0)
  - open_file_ios (0.0.1):
    - Flutter
  - package_info_plus (0.4.5):
    - Flutter
  - pasteboard (0.0.1):
    - Flutter
  - path_provider_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - permission_handler_apple (9.1.1):
    - Flutter
  - photo_manager (3.7.1):
    - Flutter
    - FlutterMacOS
  - PINCache (3.0.4):
    - PINCache/Arc-exception-safe (= 3.0.4)
    - PINCache/Core (= 3.0.4)
  - PINCache/Arc-exception-safe (3.0.4):
    - PINCache/Core
  - PINCache/Core (3.0.4):
    - PINOperation (~> 1.2.3)
  - PINOperation (1.2.3)
  - RTCRoomEngine/Professional (2.9.5):
    - TXIMSDK_Plus_iOS_XCFramework (>= 8.1.6116)
    - TXLiteAVSDK_Professional (>= 12.2.16956)
  - SDWebImage (5.21.1):
    - SDWebImage/Core (= 5.21.1)
  - SDWebImage/Core (5.21.1)
  - SDWebImageWebPCoder (0.14.6):
    - libwebp (~> 1.0)
    - SDWebImage/Core (~> 5.17)
  - shared_preferences_foundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - SnapKit (5.7.1)
  - sqflite_darwin (0.0.4):
    - Flutter
    - FlutterMacOS
  - SwiftyGif (5.4.5)
  - tencent_calls_uikit (0.0.1):
    - Flutter
    - RTCRoomEngine/Professional (~> 2.9.2)
    - SnapKit
    - TUICore (>= 8.4.6667)
  - tencent_cloud_chat_push (8.6.7019):
    - Flutter
    - TIMPush (= 8.6.7019)
    - TXIMSDK_Plus_iOS_XCFramework
  - tencent_cloud_chat_sdk (8.0.0):
    - Flutter
    - HydraAsync
    - TXIMSDK_Plus_iOS_XCFramework (= 8.6.7019)
  - tencent_cloud_uikit_core (0.0.1):
    - Flutter
  - TIMPush (8.6.7019):
    - TXIMSDK_Plus_iOS_XCFramework (>= 8.6.7019)
  - Toast (4.1.1)
  - TOCropViewController (2.7.4)
  - TUICore (8.6.7019):
    - SDWebImage
    - TUICore/ImSDK_Plus (= 8.6.7019)
  - TUICore/Base (8.6.7019):
    - SDWebImage
  - TUICore/ImSDK_Plus (8.6.7019):
    - SDWebImage
    - TUICore/Base
    - TXIMSDK_Plus_iOS_XCFramework
  - TXIMSDK_Plus_iOS_XCFramework (8.6.7019)
  - TXLiteAVSDK_Professional (12.6.18866):
    - TXLiteAVSDK_Professional/Professional (= 12.6.18866)
  - TXLiteAVSDK_Professional/Professional (12.6.18866)
  - url_launcher_ios (0.0.1):
    - Flutter
  - video_player_avfoundation (0.0.1):
    - Flutter
    - FlutterMacOS
  - wakelock_plus (0.0.1):
    - Flutter
  - webview_flutter_wkwebview (0.0.1):
    - Flutter

DEPENDENCIES:
  - audio_session (from `.symlinks/plugins/audio_session/ios`)
  - better_player_plus (from `.symlinks/plugins/better_player_plus/ios`)
  - device_info_plus (from `.symlinks/plugins/device_info_plus/ios`)
  - fc_native_video_thumbnail (from `.symlinks/plugins/fc_native_video_thumbnail/darwin`)
  - file_picker (from `.symlinks/plugins/file_picker/ios`)
  - Flutter (from `Flutter`)
  - flutter_image_compress_common (from `.symlinks/plugins/flutter_image_compress_common/ios`)
  - flutter_native_splash (from `.symlinks/plugins/flutter_native_splash/ios`)
  - flutter_plugin_record_plus (from `.symlinks/plugins/flutter_plugin_record_plus/ios`)
  - fluttertoast (from `.symlinks/plugins/fluttertoast/ios`)
  - image_cropper (from `.symlinks/plugins/image_cropper/ios`)
  - image_gallery_saver_plus (from `.symlinks/plugins/image_gallery_saver_plus/ios`)
  - image_picker_ios (from `.symlinks/plugins/image_picker_ios/ios`)
  - just_audio (from `.symlinks/plugins/just_audio/darwin`)
  - open_file_ios (from `.symlinks/plugins/open_file_ios/ios`)
  - package_info_plus (from `.symlinks/plugins/package_info_plus/ios`)
  - pasteboard (from `.symlinks/plugins/pasteboard/ios`)
  - path_provider_foundation (from `.symlinks/plugins/path_provider_foundation/darwin`)
  - permission_handler_apple (from `.symlinks/plugins/permission_handler_apple/ios`)
  - photo_manager (from `.symlinks/plugins/photo_manager/ios`)
  - shared_preferences_foundation (from `.symlinks/plugins/shared_preferences_foundation/darwin`)
  - sqflite_darwin (from `.symlinks/plugins/sqflite_darwin/darwin`)
  - tencent_calls_uikit (from `.symlinks/plugins/tencent_calls_uikit/ios`)
  - tencent_cloud_chat_push (from `.symlinks/plugins/tencent_cloud_chat_push/ios`)
  - tencent_cloud_chat_sdk (from `.symlinks/plugins/tencent_cloud_chat_sdk/ios`)
  - tencent_cloud_uikit_core (from `.symlinks/plugins/tencent_cloud_uikit_core/ios`)
  - url_launcher_ios (from `.symlinks/plugins/url_launcher_ios/ios`)
  - video_player_avfoundation (from `.symlinks/plugins/video_player_avfoundation/darwin`)
  - wakelock_plus (from `.symlinks/plugins/wakelock_plus/ios`)
  - webview_flutter_wkwebview (from `.symlinks/plugins/webview_flutter_wkwebview/ios`)

SPEC REPOS:
  trunk:
    - Cache
    - DKImagePickerController
    - DKPhotoGallery
    - GCDWebServer
    - HLSCachingReverseProxyServer
    - HydraAsync
    - libwebp
    - Mantle
    - PINCache
    - PINOperation
    - RTCRoomEngine
    - SDWebImage
    - SDWebImageWebPCoder
    - SnapKit
    - SwiftyGif
    - TIMPush
    - Toast
    - TOCropViewController
    - TUICore
    - TXIMSDK_Plus_iOS_XCFramework
    - TXLiteAVSDK_Professional

EXTERNAL SOURCES:
  audio_session:
    :path: ".symlinks/plugins/audio_session/ios"
  better_player_plus:
    :path: ".symlinks/plugins/better_player_plus/ios"
  device_info_plus:
    :path: ".symlinks/plugins/device_info_plus/ios"
  fc_native_video_thumbnail:
    :path: ".symlinks/plugins/fc_native_video_thumbnail/darwin"
  file_picker:
    :path: ".symlinks/plugins/file_picker/ios"
  Flutter:
    :path: Flutter
  flutter_image_compress_common:
    :path: ".symlinks/plugins/flutter_image_compress_common/ios"
  flutter_native_splash:
    :path: ".symlinks/plugins/flutter_native_splash/ios"
  flutter_plugin_record_plus:
    :path: ".symlinks/plugins/flutter_plugin_record_plus/ios"
  fluttertoast:
    :path: ".symlinks/plugins/fluttertoast/ios"
  image_cropper:
    :path: ".symlinks/plugins/image_cropper/ios"
  image_gallery_saver_plus:
    :path: ".symlinks/plugins/image_gallery_saver_plus/ios"
  image_picker_ios:
    :path: ".symlinks/plugins/image_picker_ios/ios"
  just_audio:
    :path: ".symlinks/plugins/just_audio/darwin"
  open_file_ios:
    :path: ".symlinks/plugins/open_file_ios/ios"
  package_info_plus:
    :path: ".symlinks/plugins/package_info_plus/ios"
  pasteboard:
    :path: ".symlinks/plugins/pasteboard/ios"
  path_provider_foundation:
    :path: ".symlinks/plugins/path_provider_foundation/darwin"
  permission_handler_apple:
    :path: ".symlinks/plugins/permission_handler_apple/ios"
  photo_manager:
    :path: ".symlinks/plugins/photo_manager/ios"
  shared_preferences_foundation:
    :path: ".symlinks/plugins/shared_preferences_foundation/darwin"
  sqflite_darwin:
    :path: ".symlinks/plugins/sqflite_darwin/darwin"
  tencent_calls_uikit:
    :path: ".symlinks/plugins/tencent_calls_uikit/ios"
  tencent_cloud_chat_push:
    :path: ".symlinks/plugins/tencent_cloud_chat_push/ios"
  tencent_cloud_chat_sdk:
    :path: ".symlinks/plugins/tencent_cloud_chat_sdk/ios"
  tencent_cloud_uikit_core:
    :path: ".symlinks/plugins/tencent_cloud_uikit_core/ios"
  url_launcher_ios:
    :path: ".symlinks/plugins/url_launcher_ios/ios"
  video_player_avfoundation:
    :path: ".symlinks/plugins/video_player_avfoundation/darwin"
  wakelock_plus:
    :path: ".symlinks/plugins/wakelock_plus/ios"
  webview_flutter_wkwebview:
    :path: ".symlinks/plugins/webview_flutter_wkwebview/ios"

SPEC CHECKSUMS:
  audio_session: 9bb7f6c970f21241b19f5a3658097ae459681ba0
  better_player_plus: 10794c0ed1b3b4ae058939e22a6172f850a2039b
  Cache: 4ca7e00363fca5455f26534e5607634c820ffc2d
  device_info_plus: 71ffc6ab7634ade6267c7a93088ed7e4f74e5896
  DKImagePickerController: 946cec48c7873164274ecc4624d19e3da4c1ef3c
  DKPhotoGallery: b3834fecb755ee09a593d7c9e389d8b5d6deed60
  fc_native_video_thumbnail: b511cec81fad66be9b28dd54b9adb39d40fcd6cc
  file_picker: 5f42b9d5580e30b57b4863f9d94b448016b702e5
  Flutter: e0871f40cf51350855a761d2e70bf5af5b9b5de7
  flutter_image_compress_common: 1697a328fd72bfb335507c6bca1a65fa5ad87df1
  flutter_native_splash: 6cad9122ea0fad137d23137dd14b937f3e90b145
  flutter_plugin_record_plus: 7dc36c7574e26041729728fdaaf7eeba30d90ec0
  fluttertoast: 76fea30fcf04176325f6864c87306927bd7d2038
  GCDWebServer: 2c156a56c8226e2d5c0c3f208a3621ccffbe3ce4
  HLSCachingReverseProxyServer: 59935e1e0244ad7f3375d75b5ef46e8eb26ab181
  HydraAsync: 8d589bd725b0224f899afafc9a396327405f8063
  image_cropper: c4326ea50132b1e1564499e5d32a84f01fb03537
  image_gallery_saver_plus: e597bf65a7846979417a3eae0763b71b6dfec6c3
  image_picker_ios: 7fe1ff8e34c1790d6fff70a32484959f563a928a
  just_audio: 4e391f57b79cad2b0674030a00453ca5ce817eed
  libwebp: 02b23773aedb6ff1fd38cec7a77b81414c6842a8
  Mantle: c5aa8794a29a022dfbbfc9799af95f477a69b62d
  open_file_ios: 5ff7526df64e4394b4fe207636b67a95e83078bb
  package_info_plus: af8e2ca6888548050f16fa2f1938db7b5a5df499
  pasteboard: 49088aeb6119d51f976a421db60d8e1ab079b63c
  path_provider_foundation: 080d55be775b7414fd5a5ef3ac137b97b097e564
  permission_handler_apple: 3787117e48f80715ff04a3830ca039283d6a4f29
  photo_manager: 1d80ae07a89a67dfbcae95953a1e5a24af7c3e62
  PINCache: d9a87a0ff397acffe9e2f0db972ac14680441158
  PINOperation: fb563bcc9c32c26d6c78aaff967d405aa2ee74a7
  RTCRoomEngine: acf512ad2bb7bce914c5e248ef5837e7b4d47abd
  SDWebImage: f29024626962457f3470184232766516dee8dfea
  SDWebImageWebPCoder: e38c0a70396191361d60c092933e22c20d5b1380
  shared_preferences_foundation: 9e1978ff2562383bd5676f64ec4e9aa8fa06a6f7
  SnapKit: d612e99e678a2d3b95bf60b0705ed0a35c03484a
  sqflite_darwin: 20b2a3a3b70e43edae938624ce550a3cbf66a3d0
  SwiftyGif: 706c60cf65fa2bc5ee0313beece843c8eb8194d4
  tencent_calls_uikit: e3803df89cf5d6b2911f842f8abb39660bff425d
  tencent_cloud_chat_push: f87ae58098c2062b06e81f39fc53afc528395916
  tencent_cloud_chat_sdk: 2797f99ea8ee1cd9933b9ac866a42575a85fac7a
  tencent_cloud_uikit_core: 137e8ae40882b1929508e688182b2818708cc078
  TIMPush: d0dfe96355ee413a7cacb2576f8aaa66f6073ab2
  Toast: 1f5ea13423a1e6674c4abdac5be53587ae481c4e
  TOCropViewController: 80b8985ad794298fb69d3341de183f33d1853654
  TUICore: 7991cb71071c1c75360d58f92250fa4db2313a76
  TXIMSDK_Plus_iOS_XCFramework: cb54f7de6e30e1368c6831c6eff31c25393bbb98
  TXLiteAVSDK_Professional: 33a5d8da98797efaba8e026c4e3e6c7ecf75f0d3
  url_launcher_ios: 694010445543906933d732453a59da0a173ae33d
  video_player_avfoundation: 2cef49524dd1f16c5300b9cd6efd9611ce03639b
  wakelock_plus: e29112ab3ef0b318e58cfa5c32326458be66b556
  webview_flutter_wkwebview: 6e6160e04b1e85872253adc5322afe416d9cdddc

PODFILE CHECKSUM: 9b5fec7154ddb2ccddc6154ed5da4b8dfb10878f

COCOAPODS: 1.16.2
